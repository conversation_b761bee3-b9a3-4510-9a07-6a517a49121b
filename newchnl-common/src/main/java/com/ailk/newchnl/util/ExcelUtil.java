/*
 * $Id: ExcelUtil.java,v 1.12 2015/09/06 09:02:38 fuqiang Exp $
 *
 * Copyright 2014 Asiainfo Technologies(China),Inc. All rights reserved.
 */
package com.ailk.newchnl.util;

import org.apache.commons.beanutils.ConvertUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.CellRangeAddress;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 * @version $Id: ExcelUtil.java,v 1.12 2015/09/06 09:02:38 fuqiang Exp $
 * Created on 2014年7月31日 下午8:44:55
 */
public class ExcelUtil {
    private static final Logger logger = LoggerFactory.getLogger(ExcelUtil.class);

    /**
     * excel类型为 xls
     * excel版本为2003
     */
    public final static int XLS = 1;

    /**
     * excel类型为xlsx
     * excel版本为2007以后
     */
    public final static int XLSX = 2;


    public static int getExcelType(String filePath) {
        if (isXLS(filePath)) {
            return XLS;
        } else if (isXLSX(filePath)) {
            return XLSX;
        }
        return XLSX;
    }

    public static boolean isXLS(String filePath) {
        return filePath.matches("^.+\\.(?i)(xls)$");
    }

    public static boolean isXLSX(String filePath) {
        return filePath.matches("^.+\\.(?i)(xlsx)$");
    }


    /**
     * 导出根据dataGird 导出excel
     *
     * @param excelType
     * @param sheetName
     * @param formatDataMap
     * @param columns
     * @param rows
     * @param outputstream
     * @throws Exception
     */
    public static void exportExcelForDataGrid(
            String excelType,
            String sheetName,
            Map<String, Map<String, String>> formatDataMap,
            List<Map<String, String>> columns,
            List<Map<String, Object>> rows,
            OutputStream outputstream) throws Exception {

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Workbook wb_w = null;
        if (excelType.equals("1")) {
            wb_w = new HSSFWorkbook();
        } else {
            wb_w = new XSSFWorkbook();
        }

        CellStyle cellStyle_head = wb_w.createCellStyle();
        cellStyle_head.setAlignment(CellStyle.ALIGN_CENTER);
        Font headerFont = wb_w.createFont(); // 字体
        headerFont.setFontHeightInPoints((short) 10);
        headerFont.setFontName("微软雅黑");
        headerFont.setBoldweight(Font.BOLDWEIGHT_BOLD);
        cellStyle_head.setFont(headerFont);
        cellStyle_head.setBorderBottom((short) 1);
        cellStyle_head.setBorderLeft((short) 1);
        cellStyle_head.setBorderRight((short) 1);
        cellStyle_head.setBorderTop((short) 1);
        cellStyle_head.setWrapText(false);
        cellStyle_head.setFillPattern(CellStyle.SOLID_FOREGROUND);
        cellStyle_head.setFillForegroundColor(HSSFColor.YELLOW.index);

        CellStyle cellStyle_content_0 = wb_w.createCellStyle();
        cellStyle_content_0.setAlignment(CellStyle.ALIGN_LEFT);
        headerFont = wb_w.createFont(); // 字体
        headerFont.setFontHeightInPoints((short) 9);
        headerFont.setFontName("微软雅黑");
        cellStyle_content_0.setFont(headerFont);
        cellStyle_content_0.setBorderBottom((short) 1);
        cellStyle_content_0.setBorderLeft((short) 1);
        cellStyle_content_0.setBorderRight((short) 1);
        cellStyle_content_0.setBorderTop((short) 1);
        cellStyle_content_0.setWrapText(false);
        cellStyle_content_0.setFillPattern(CellStyle.SOLID_FOREGROUND);
        cellStyle_content_0.setFillForegroundColor(HSSFColor.LIGHT_TURQUOISE.index);

        CellStyle cellStyle_content_1 = wb_w.createCellStyle();
        cellStyle_content_1.setAlignment(CellStyle.ALIGN_LEFT);
        headerFont = wb_w.createFont(); // 字体
        headerFont.setFontHeightInPoints((short) 9);
        headerFont.setFontName("微软雅黑");
        cellStyle_content_1.setFont(headerFont);
        cellStyle_content_1.setBorderBottom((short) 1);
        cellStyle_content_1.setBorderLeft((short) 1);
        cellStyle_content_1.setBorderRight((short) 1);
        cellStyle_content_1.setBorderTop((short) 1);
        cellStyle_content_1.setWrapText(false);

        Sheet sheet_w = wb_w.createSheet(sheetName);
        //设置列宽
        for (int p = 0; p < columns.size(); p++) {
            sheet_w.setColumnWidth(p, 30 * 200);
        }

        //设置第一行 行头 head
        Row row_w = sheet_w.createRow(0);
        for (int k = 0; k < columns.size(); k++) {
            Cell cell_w = row_w.createCell(k);
            cell_w.setCellStyle(cellStyle_head);

            if (columns.get(k).get("title") != null) {
                cell_w.setCellValue("" + columns.get(k).get("title"));
            } else if (columns.get(k).get("field") != null) {
                cell_w.setCellValue("" + columns.get(k).get("field"));
            } else {
                cell_w.setCellValue("");
            }
        }

        //设置表格内容
        for (int j = 0; j < rows.size(); j++) {
            row_w = sheet_w.createRow(j + 1);
            Map<String, Object> map = rows.get(j);
            for (int k = 0; k < columns.size(); k++) {
                Cell cell_w = row_w.createCell(k);
                if (j % 2 == 1) {
                    cell_w.setCellStyle(cellStyle_content_0);
                } else {
                    cell_w.setCellStyle(cellStyle_content_1);
                }
                if (columns.get(k).get("field") != null && map.get(columns.get(k).get("field")) != null) {
                    String field = columns.get(k).get("field");
                    String value = "";
                    if (map.get(field) == null) {
                        value = "";
                    } else {
                        if (map.get(field) instanceof Date) {
                            value = formatter.format((Date) map.get(field));
                        } else {
                            value = "" + map.get(field);
                        }
                    }
                    if (value.trim().equals("null")) {
                        value = "";
                    }
                    if (formatDataMap.get(field) != null && formatDataMap.get(field).get(value) != null) {
                        cell_w.setCellValue(formatDataMap.get(field).get(value));
                    } else {
                        cell_w.setCellValue(value);
                    }
                } else {
                    cell_w.setCellValue("");
                }
            }
        }
        try {
            wb_w.write(outputstream);
            outputstream.flush();
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    /**
     * 导出根据dataGird 导出excel
     *
     * @param excelType
     * @param sheetName
     * @param formatDataMap
     * @param columns
     * @param rows
     * @param outputstream
     * @throws Exception
     */
    public static void exportExcelForDataGridHasMoreHeader(
            String excelType,
            String sheetName,
            Map<String, Map<String, String>> formatDataMap,
            List<List<Map<String, Object>>> header,
            List<Map<String, String>> columns,
            List<Map<String, Object>> rows,
            OutputStream outputstream) throws Exception {

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Workbook wb_w = null;
        if (excelType.equals("1")) {
            wb_w = new HSSFWorkbook();
        } else {
            wb_w = new XSSFWorkbook();
        }

        CellStyle cellStyle_head = wb_w.createCellStyle();
        cellStyle_head.setAlignment(CellStyle.ALIGN_CENTER);
        Font headerFont = wb_w.createFont(); // 字体
        headerFont.setFontHeightInPoints((short) 10);
        headerFont.setFontName("微软雅黑");
        headerFont.setBoldweight(Font.BOLDWEIGHT_BOLD);
        cellStyle_head.setFont(headerFont);
        cellStyle_head.setBorderBottom((short) 1);
        cellStyle_head.setBorderLeft((short) 1);
        cellStyle_head.setBorderRight((short) 1);
        cellStyle_head.setBorderTop((short) 1);
        cellStyle_head.setWrapText(false);
        cellStyle_head.setFillPattern(CellStyle.SOLID_FOREGROUND);
        cellStyle_head.setFillForegroundColor(HSSFColor.YELLOW.index);

        CellStyle cellStyle_content_0 = wb_w.createCellStyle();
        cellStyle_content_0.setAlignment(CellStyle.ALIGN_LEFT);
        headerFont = wb_w.createFont(); // 字体
        headerFont.setFontHeightInPoints((short) 9);
        headerFont.setFontName("微软雅黑");
        cellStyle_content_0.setFont(headerFont);
        cellStyle_content_0.setBorderBottom((short) 1);
        cellStyle_content_0.setBorderLeft((short) 1);
        cellStyle_content_0.setBorderRight((short) 1);
        cellStyle_content_0.setBorderTop((short) 1);
        cellStyle_content_0.setWrapText(false);
        cellStyle_content_0.setFillPattern(CellStyle.SOLID_FOREGROUND);
        cellStyle_content_0.setFillForegroundColor(HSSFColor.LIGHT_TURQUOISE.index);

        CellStyle cellStyle_content_1 = wb_w.createCellStyle();
        cellStyle_content_1.setAlignment(CellStyle.ALIGN_LEFT);
        headerFont = wb_w.createFont(); // 字体
        headerFont.setFontHeightInPoints((short) 9);
        headerFont.setFontName("微软雅黑");
        cellStyle_content_1.setFont(headerFont);
        cellStyle_content_1.setBorderBottom((short) 1);
        cellStyle_content_1.setBorderLeft((short) 1);
        cellStyle_content_1.setBorderRight((short) 1);
        cellStyle_content_1.setBorderTop((short) 1);
        cellStyle_content_1.setWrapText(false);

        Sheet sheet_w = wb_w.createSheet(sheetName);
        //设置列宽
        for (int p = 0; p < columns.size(); p++) {
            sheet_w.setColumnWidth(p, 30 * 200);
        }
        //设置多级表头
        for (int i = 0; i < header.size(); i++) {
            Row row_w = sheet_w.createRow(i);
            int j = 0;
            for (int k = 0; k < header.get(i).size(); k++) {
                Integer count = header.get(i).get(k).get("count") == null ? 1 : Integer.valueOf(header.get(i).get(k).get("count").toString());
                for (int f = 0; f < count; f++) {
                    Cell cell_w = row_w.createCell(j++);
                    cell_w.setCellStyle(cellStyle_head);
                    if (header.get(i).get(k).get("title") != null) {
                        cell_w.setCellValue("" + header.get(i).get(k).get("title"));
                    } else if (header.get(i).get(k).get("field") != null) {
                        cell_w.setCellValue("" + header.get(i).get(k).get("field"));
                    } else {
                        cell_w.setCellValue("");
                    }
                }
                sheet_w.addMergedRegion(new CellRangeAddress(i, i, j - count, j - 1));
            }
        }

        //设置第一行 行头 head
        Row row_w = sheet_w.createRow(header.size());
        for (int k = 0; k < columns.size(); k++) {
            Cell cell_w = row_w.createCell(k);
            cell_w.setCellStyle(cellStyle_head);

            if (columns.get(k).get("title") != null) {
                cell_w.setCellValue("" + columns.get(k).get("title"));
            } else if (columns.get(k).get("field") != null) {
                cell_w.setCellValue("" + columns.get(k).get("field"));
            } else {
                cell_w.setCellValue("");
            }
        }

        //设置表格内容
        for (int j = 0; j < rows.size(); j++) {
            row_w = sheet_w.createRow(j + header.size() + 1);
            Map<String, Object> map = rows.get(j);
            for (int k = 0; k < columns.size(); k++) {
                Cell cell_w = row_w.createCell(k);
                if (j % 2 == 1) {
                    cell_w.setCellStyle(cellStyle_content_0);
                } else {
                    cell_w.setCellStyle(cellStyle_content_1);
                }
                if (columns.get(k).get("field") != null && map.get(columns.get(k).get("field")) != null) {
                    String field = columns.get(k).get("field");
                    String value = "";
                    if (map.get(field) == null) {
                        value = "";
                    } else {
                        if (map.get(field) instanceof Date) {
                            value = formatter.format((Date) map.get(field));
                        } else {
                            value = "" + map.get(field);
                        }
                    }
                    if (value.trim().equals("null")) {
                        value = "";
                    }
                    if (formatDataMap.get(field) != null && formatDataMap.get(field).get(value) != null) {
                        cell_w.setCellValue(formatDataMap.get(field).get(value));
                    } else {
                        cell_w.setCellValue(value);
                    }
                } else {
                    cell_w.setCellValue("");
                }
            }
        }
        try {
            wb_w.write(outputstream);
            outputstream.flush();
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }
    /**
     * 导出Excel包含多个sheet界面
     * @param sheetName
     * @param columns
     * @param rows
     * @throws Exception
     */

    public static void exportExcelForDataGridHasMoreHeader1(
            String sheetName,
            XSSFWorkbook workbook,
            int sheetNum,
            Map<String, Map<String, String>> formatDataMap,
            List<List<Map<String, Object>>> header,
            List<Map<String, String>> columns,
            List<Map<String, Object>> rows) throws Exception {

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");


        CellStyle cellStyle_head = workbook.createCellStyle();
        cellStyle_head.setAlignment(CellStyle.ALIGN_CENTER);
        Font headerFont = workbook.createFont(); // 字体
        headerFont.setFontHeightInPoints((short) 10);
        headerFont.setFontName("微软雅黑");
        headerFont.setBoldweight(Font.BOLDWEIGHT_BOLD);
        cellStyle_head.setFont(headerFont);
        cellStyle_head.setBorderBottom((short) 1);
        cellStyle_head.setBorderLeft((short) 1);
        cellStyle_head.setBorderRight((short) 1);
        cellStyle_head.setBorderTop((short) 1);
        cellStyle_head.setWrapText(false);
        cellStyle_head.setFillPattern(CellStyle.SOLID_FOREGROUND);
        cellStyle_head.setFillForegroundColor(HSSFColor.WHITE.index);

        CellStyle cellStyle_content_0 = workbook.createCellStyle();
        cellStyle_content_0.setAlignment(CellStyle.ALIGN_LEFT);
        headerFont = workbook.createFont(); // 字体
        headerFont.setFontHeightInPoints((short) 9);
        headerFont.setFontName("微软雅黑");
        cellStyle_content_0.setFont(headerFont);
        cellStyle_content_0.setBorderBottom((short) 1);
        cellStyle_content_0.setBorderLeft((short) 1);
        cellStyle_content_0.setBorderRight((short) 1);
        cellStyle_content_0.setBorderTop((short) 1);
        cellStyle_content_0.setWrapText(false);
        cellStyle_content_0.setFillPattern(CellStyle.SOLID_FOREGROUND);
        cellStyle_content_0.setFillForegroundColor(HSSFColor.WHITE.index);

        CellStyle cellStyle_content_1 = workbook.createCellStyle();
        cellStyle_content_1.setAlignment(CellStyle.ALIGN_LEFT);
        headerFont = workbook.createFont(); // 字体
        headerFont.setFontHeightInPoints((short) 9);
        headerFont.setFontName("微软雅黑");
        cellStyle_content_1.setFont(headerFont);
        cellStyle_content_1.setBorderBottom((short) 1);
        cellStyle_content_1.setBorderLeft((short) 1);
        cellStyle_content_1.setBorderRight((short) 1);
        cellStyle_content_1.setBorderTop((short) 1);
        cellStyle_content_1.setWrapText(false);

        //Sheet sheet_w = workbook.createSheet(sheetName);

        XSSFSheet sheet_w = workbook.createSheet();
        workbook.setSheetName(sheetNum, sheetName);
        //设置列宽
        for (int p = 0; p < columns.size(); p++) {
            sheet_w.setColumnWidth(p, 30 * 200);
        }
        //设置多级表头
        for (int i = 0; i < header.size(); i++) {
            Row row_w = sheet_w.createRow(i);
            int j = 0;
            for (int k = 0; k < header.get(i).size(); k++) {
                Integer count = header.get(i).get(k).get("count") == null ? 1 : Integer.valueOf(header.get(i).get(k).get("count").toString());
                for (int f = 0; f < count; f++) {
                    Cell cell_w = row_w.createCell(j++);
                    cell_w.setCellStyle(cellStyle_head);
                    if (header.get(i).get(k).get("title") != null) {
                        cell_w.setCellValue("" + header.get(i).get(k).get("title"));
                    } else if (header.get(i).get(k).get("field") != null) {
                        cell_w.setCellValue("" + header.get(i).get(k).get("field"));
                    } else {
                        cell_w.setCellValue("");
                    }
                }
                sheet_w.addMergedRegion(new CellRangeAddress(i, i, j - count, j - 1));
            }
        }

        //设置第一行 行头 head
        Row row_w = sheet_w.createRow(header.size());
        for (int k = 0; k < columns.size(); k++) {
            Cell cell_w = row_w.createCell(k);
            cell_w.setCellStyle(cellStyle_head);

            if (columns.get(k).get("title") != null) {
                cell_w.setCellValue("" + columns.get(k).get("title"));
            } else if (columns.get(k).get("field") != null) {
                cell_w.setCellValue("" + columns.get(k).get("field"));
            } else {
                cell_w.setCellValue("");
            }
        }

        //设置表格内容
        for (int j = 0; j < rows.size(); j++) {
            row_w = sheet_w.createRow(j + header.size() + 1);
            Map<String, Object> map = rows.get(j);
            for (int k = 0; k < columns.size(); k++) {
                Cell cell_w = row_w.createCell(k);
                if (j % 2 == 1) {
                    cell_w.setCellStyle(cellStyle_content_0);
                } else {
                    cell_w.setCellStyle(cellStyle_content_1);
                }
                if (columns.get(k).get("field") != null && map.get(columns.get(k).get("field")) != null) {
                    String field = columns.get(k).get("field");
                    String value = "";
                    if (map.get(field) == null) {
                        value = "";
                    } else {
                        if (map.get(field) instanceof Date) {
                            value = formatter.format((Date) map.get(field));
                        } else {
                            value = "" + map.get(field);
                        }
                    }
                    if (value.trim().equals("null")) {
                        value = "";
                    }
                    if (formatDataMap.get(field) != null && formatDataMap.get(field).get(value) != null) {
                        cell_w.setCellValue(formatDataMap.get(field).get(value));
                    } else {
                        cell_w.setCellValue(value);
                    }
                } else {
                    cell_w.setCellValue("");
                }
            }
        }

    }

    /**
     * 导出Excel
     * @param excelType
     * @param sheetName
     * @param columns
     * @param rows
     * @param outputstream
     * @throws Exception
     */
    public static void exportExcel(
            String excelType,
            String sheetName,
            List<String> columns,
            List<List<Object>> rows,
            OutputStream outputstream) throws Exception {

        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        Workbook wb_w = null;
        if (excelType.equals("1")) {
            wb_w = new HSSFWorkbook();
        } else {
            wb_w = new XSSFWorkbook();
        }

        CellStyle cellStyle_head = wb_w.createCellStyle();
        cellStyle_head.setAlignment(CellStyle.ALIGN_CENTER);
        Font headerFont = wb_w.createFont(); // 字体
        headerFont.setFontHeightInPoints((short) 10);
        headerFont.setFontName("微软雅黑");
        headerFont.setBoldweight(Font.BOLDWEIGHT_BOLD);
        cellStyle_head.setFont(headerFont);
        cellStyle_head.setBorderBottom((short) 1);
        cellStyle_head.setBorderLeft((short) 1);
        cellStyle_head.setBorderRight((short) 1);
        cellStyle_head.setBorderTop((short) 1);
        cellStyle_head.setWrapText(false);
        cellStyle_head.setFillPattern(CellStyle.SOLID_FOREGROUND);
        cellStyle_head.setFillForegroundColor(HSSFColor.YELLOW.index);

        CellStyle cellStyle_content_0 = wb_w.createCellStyle();
        cellStyle_content_0.setAlignment(CellStyle.ALIGN_LEFT);
        headerFont = wb_w.createFont(); // 字体
        headerFont.setFontHeightInPoints((short) 9);
        headerFont.setFontName("微软雅黑");
        cellStyle_content_0.setFont(headerFont);
        cellStyle_content_0.setBorderBottom((short) 1);
        cellStyle_content_0.setBorderLeft((short) 1);
        cellStyle_content_0.setBorderRight((short) 1);
        cellStyle_content_0.setBorderTop((short) 1);
        cellStyle_content_0.setWrapText(false);
        cellStyle_content_0.setFillPattern(CellStyle.SOLID_FOREGROUND);
        cellStyle_content_0.setFillForegroundColor(HSSFColor.LIGHT_TURQUOISE.index);

        CellStyle cellStyle_content_1 = wb_w.createCellStyle();
        cellStyle_content_1.setAlignment(CellStyle.ALIGN_LEFT);
        headerFont = wb_w.createFont(); // 字体
        headerFont.setFontHeightInPoints((short) 9);
        headerFont.setFontName("微软雅黑");
        cellStyle_content_1.setFont(headerFont);
        cellStyle_content_1.setBorderBottom((short) 1);
        cellStyle_content_1.setBorderLeft((short) 1);
        cellStyle_content_1.setBorderRight((short) 1);
        cellStyle_content_1.setBorderTop((short) 1);
        cellStyle_content_1.setWrapText(false);

        Sheet sheet_w = wb_w.createSheet(sheetName);
        //设置列宽
        for (int p = 0; p < columns.size(); p++) {
            sheet_w.setColumnWidth(p, 30 * 200);
        }

        //设置第一行 行头 head
        Row row_w = sheet_w.createRow(0);
        for (int k = 0; k < columns.size(); k++) {
            Cell cell_w = row_w.createCell(k);
            cell_w.setCellStyle(cellStyle_head);
            cell_w.setCellValue(columns.get(k));
        }

        //设置表格内容
        for (int j = 0; j < rows.size(); j++) {
            row_w = sheet_w.createRow(j + 1);
            List<Object> row = rows.get(j);
            for (int k = 0; k < row.size(); k++) {
                Cell cell_w = row_w.createCell(k);
                if (j % 2 == 1) {
                    cell_w.setCellStyle(cellStyle_content_0);
                } else {
                    cell_w.setCellStyle(cellStyle_content_1);
                }
                if (row.get(k) != null) {
                    String value = "";
                    if (row.get(k) instanceof Date) {
                        value = formatter.format((Date) row.get(k));
                    } else if (row.get(k) instanceof Double) {
                        Double doubleValue = (Double) row.get(k);
                        // 如果是整数，去掉小数点
                        if (doubleValue == doubleValue.intValue()) {
                            value = String.valueOf(doubleValue.intValue());
                        } else {
                            value = String.valueOf(doubleValue);
                        }
                    } else {
                        value = "" + row.get(k);
                    }
                    cell_w.setCellValue(value);
                } else {
                    cell_w.setCellValue("");
                }
            }
        }
        try {
            wb_w.write(outputstream);
            outputstream.flush();
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }


    /**
     * 读取excel
     *
     * @param fileType
     * @param in
     * @return 返回 sheet 每行 每列
     * @throws Exception
     */
    public static List<List<List<Object>>> readExcel(int fileType, InputStream in) throws Exception {
        List<List<List<Object>>> fileData = new ArrayList<List<List<Object>>>();

        Workbook wb = null;
        if (fileType == XLS) {
            wb = new HSSFWorkbook(in);
        } else {
            wb = new XSSFWorkbook(in);
        }
        for (int i = 0; i < wb.getNumberOfSheets(); i++) {
            Sheet sheet = wb.getSheetAt(i);
            List<List<Object>> rowList = new ArrayList<List<Object>>();
            for (int j = 1; j < sheet.getPhysicalNumberOfRows(); j++) {
                Row row = sheet.getRow(j);
                List<Object> cellList = new ArrayList<Object>();
                for (int k = 0; k < row.getPhysicalNumberOfCells(); k++) {
                    if (row.getCell(k) != null) {
                        switch (row.getCell(k).getCellType()) {
                            case Cell.CELL_TYPE_BLANK:
                                cellList.add("");
                                break;
                            case Cell.CELL_TYPE_BOOLEAN:
                                cellList.add(row.getCell(k).getBooleanCellValue());
                                break;
                            case Cell.CELL_TYPE_STRING:
                                cellList.add(row.getCell(k).getStringCellValue());
                                break;
                            case Cell.CELL_TYPE_NUMERIC:
                                if (DateUtil.isCellDateFormatted(row.getCell(k))) {
                                    Date date = DateUtil.getJavaDate(row.getCell(k).getNumericCellValue());
                                    cellList.add(date);
                                } else {
                                    cellList.add(row.getCell(k).getNumericCellValue());
                                }
                                break;
                            case Cell.CELL_TYPE_FORMULA:
                                cellList.add(row.getCell(k).getCellFormula());
                                break;
                            case Cell.CELL_TYPE_ERROR:
                                cellList.add("" + row.getCell(k).getErrorCellValue());
                                break;
                        }
                    } else {
                        cellList.add(null);
                    }
                }
                rowList.add(cellList);
            }
            fileData.add(rowList);
        }

        return fileData;
    }

    /**
     * 读取excel
     *
     * @param fileType
     * @param in
     * @return 返回 sheet 每行 每列
     * @throws Exception
     */
    public static List<List<List<Object>>> readExcelWithHeader(int fileType, InputStream in) throws Exception {
        List<List<List<Object>>> fileData = new ArrayList<List<List<Object>>>();

        Workbook wb = null;
        if (fileType == XLS) {
            wb = new HSSFWorkbook(in);
        } else {
            wb = new XSSFWorkbook(in);
        }
        for (int i = 0; i < wb.getNumberOfSheets(); i++) {
            Sheet sheet = wb.getSheetAt(i);
            List<List<Object>> rowList = new ArrayList<List<Object>>();
            for (int j = 0; j < sheet.getPhysicalNumberOfRows(); j++) {
                Row row = sheet.getRow(j);
                List<Object> cellList = new ArrayList<Object>();
                if (row != null){
                    for (int k = 0; k < row.getPhysicalNumberOfCells(); k++) {
                        if (row.getCell(k) != null) {
                            switch (row.getCell(k).getCellType()) {
                                case Cell.CELL_TYPE_BLANK:
                                    cellList.add("");
                                    break;
                                case Cell.CELL_TYPE_BOOLEAN:
                                    cellList.add(row.getCell(k).getBooleanCellValue());
                                    break;
                                case Cell.CELL_TYPE_STRING:
                                    cellList.add(row.getCell(k).getStringCellValue());
                                    break;
                                case Cell.CELL_TYPE_NUMERIC:
                                    if (DateUtil.isCellDateFormatted(row.getCell(k))) {
                                        Date date = DateUtil.getJavaDate(row.getCell(k).getNumericCellValue());
                                        cellList.add(date);
                                    } else {
                                        cellList.add(row.getCell(k).getNumericCellValue());
                                    }
                                    break;
                                case Cell.CELL_TYPE_FORMULA:
                                    cellList.add(row.getCell(k).getCellFormula());
                                    break;
                                case Cell.CELL_TYPE_ERROR:
                                    cellList.add("" + row.getCell(k).getErrorCellValue());
                                    break;
                            }
                        } else {
                            cellList.add(null);
                        }
                    }
                }
                rowList.add(cellList);
            }
            fileData.add(rowList);
        }

        return fileData;
    }

    /**
     * 返回结果为：sheet(0)--> list --> List<T>
     *
     * @param fileType
     * @param in
     * @return
     * @throws Exception
     */
    public static <T> List<T> readExcel4Import(int fileType, InputStream in, Properties properties, Class<T> clazz) throws Exception {

        DecimalFormat df = new DecimalFormat();
        df.setMaximumFractionDigits(0);
        df.setMinimumFractionDigits(0);


        Workbook wb = null;
        if (fileType == XLS) {
            wb = new HSSFWorkbook(in);
        } else {
            wb = new XSSFWorkbook(in);
        }
        Sheet sheet = wb.getSheetAt(0);
        List<T> rowMap = new ArrayList<T>();
//		int cellCount  = 0 ;
        for (int j = 1; j < sheet.getPhysicalNumberOfRows(); j++) {
            Row row = sheet.getRow(j);
            Map<String, Object> cellMap = new HashMap<String, Object>();
            T obj = null;
            if (null == row) {
                continue;
            }
//			if(cellCount!=0&&cellCount!=row.getLastCellNum()){
//				continue;
//			}
//			cellCount = row.getLastCellNum();

            boolean isnull = true;
            for (int k = 0; k < row.getLastCellNum(); k++) {

                String excelHead = sheet.getRow(0).getCell(k).getStringCellValue();
                if (properties.containsKey(excelHead)) {
                    excelHead = properties.getProperty(excelHead);
                }
                if (row.getCell(k) != null) {
                    //导入的excel表格处理经纬度会默认转成整数，小数部分丢失，因此将这两个字段处理成字符串保存原有位数
                    if (excelHead.equals("longitude") || excelHead.equals("latitude"))
                    {
                        row.getCell(k).setCellType(1);
                    }
                    /* if里同时嵌套 if和Switch 代码没走满足里面if的条件，所以将原先的Switch改成if...else..
                    switch (row.getCell(k).getCellType()) {
                        case Cell.CELL_TYPE_BLANK:
                            cellMap.put(excelHead, "");
                            break;
                        case Cell.CELL_TYPE_BOOLEAN:
                            cellMap.put(excelHead, row.getCell(k).getBooleanCellValue());
                            isnull = false;
                            break;
                        case Cell.CELL_TYPE_STRING:
                            cellMap.put(excelHead, row.getCell(k).getStringCellValue());
                            isnull = false;
                            break;
                        case Cell.CELL_TYPE_NUMERIC:
                            if (DateUtil.isCellDateFormatted(row.getCell(k))) {
                                Date date = DateUtil.getJavaDate(row.getCell(k).getNumericCellValue());
                                isnull = false;
                                cellMap.put(excelHead, date);
                            } else {
                                cellMap.put(excelHead, df.format(row.getCell(k).getNumericCellValue()).replaceAll(",", ""));
                                isnull = false;
                            }
                            break;
                        case Cell.CELL_TYPE_FORMULA:
                            cellMap.put(excelHead, row.getCell(k).getCellFormula());
                            isnull = false;
                            break;
                        case Cell.CELL_TYPE_ERROR:
                            cellMap.put(excelHead, "" + row.getCell(k).getErrorCellValue());
                            break;
                            */

                        if(row.getCell(k).getCellType() == 3){
                        cellMap.put(excelHead, "");
                    }else if(row.getCell(k).getCellType() == 4){
                        cellMap.put(excelHead, row.getCell(k).getBooleanCellValue());
                        isnull = false;
                    }else if(row.getCell(k).getCellType() == 1){
                        cellMap.put(excelHead, row.getCell(k).getStringCellValue());
                        isnull = false;
                    }else if(row.getCell(k).getCellType() == 0){
                        if (DateUtil.isCellDateFormatted(row.getCell(k))) {
                            Date date = DateUtil.getJavaDate(row.getCell(k).getNumericCellValue());
                            isnull = false;
                            cellMap.put(excelHead, date);
                        } else {
                            cellMap.put(excelHead, df.format(row.getCell(k).getNumericCellValue()).replaceAll(",", ""));
                            isnull = false;
                        }
                    }else if(row.getCell(k).getCellType() == 2){
                        cellMap.put(excelHead, row.getCell(k).getCellFormula());
                        isnull = false;
                    }else if(row.getCell(k).getCellType() == 5){
                        cellMap.put(excelHead, "" + row.getCell(k).getErrorCellValue());
                    }
                } else {
                    cellMap.put(excelHead, null);
                }
            }
            if (isnull) {
                continue;
            }
            obj = clazz.newInstance();
            Field[] fields = clazz.getDeclaredFields();
            for (String strField : cellMap.keySet()) {
                for (Field field : fields) {
                    if (field.getName().equals(strField.trim())) {
                        // 取消 Java 语言访问检查
                        field.setAccessible(true);
                        if (null == cellMap.get(strField) || "".equals(cellMap.get(strField))) {
                            continue;
                        }
                        // 如果有ChannelSysBaseTypeAnnotation注解，则获取需要转换的codeid
                        if (field.isAnnotationPresent(com.ailk.newchnl.util.ChannelSysBaseTypeAnnotation.class)) {
                            ChannelSysBaseTypeAnnotation anno = field.getAnnotation(ChannelSysBaseTypeAnnotation.class);
                            Object longOrInteger = ChannelSysBaseTypeUtil.getCodeId(anno.codeType(), cellMap.get(strField).toString());
                            if (null == longOrInteger) {
                                continue;
                            }
                            if (field.getType().getName().equals(Long.class.getName())) {
                                longOrInteger = Long.parseLong(longOrInteger.toString());
                            } else if (field.getType().getName().equals(Integer.class.getName())) {
                                longOrInteger = Integer.parseInt(longOrInteger.toString());
                            } else {
                                longOrInteger = longOrInteger.toString();
                            }
                            field.set(obj, longOrInteger);
                            continue;
                        }

                        Object tempObj = new Object();
                        if (field.getType().getName().equals(String.class.getName())) {
                            tempObj = String.valueOf(cellMap.get(strField));
                        } else if (field.getType().getName().equals(Long.class.getName())) {
                            Double dField = null;
							/*if(cellMap.get(strField) instanceof Double){
								dField = (Double)cellMap.get(strField);
							}
							if(cellMap.get(strField) instanceof String){
								dField = Double.parseDouble(cellMap.get(strField).toString());
							}*/
                            dField = (Double) ConvertUtils.convert(cellMap.get(strField), Double.class);
                            if (null == dField) {
                                continue;
                            }
                            tempObj = Long.valueOf(dField.intValue());
                        } else if (field.getType().getName().equals(Double.class.getName())) {
                            tempObj = Double.parseDouble(cellMap.get(strField).toString());
                        } else if (field.getType().getName().equals(int.class.getName())) {
                            Double doub = Double.valueOf(cellMap.get(strField).toString());
                            tempObj = doub.intValue();
                        } else if (field.getType().getName().equals(Integer.class.getName())) {
                            Double doub = Double.valueOf(cellMap.get(strField).toString());
                            tempObj = doub.intValue();
                        } else if (field.getType().getName().equals(Date.class.getName())) {
                            tempObj = cellMap.get(strField);
                        }
                        field.set(obj, tempObj);
                    }
                }
            }
            rowMap.add(obj);
        }
        return rowMap;
    }

    /**
     * 读取excel --针对新加的功能点做的。私有化  huangdy
     *
     * @param fileType
     * @param in
     * @return 返回 sheet 每行 每列
     * @throws Exception
     */
    public static List<List<List<Object>>> readExcelAdjust(int fileType, InputStream in) throws Exception {
        List<List<List<Object>>> fileData = new ArrayList<List<List<Object>>>();

        Workbook wb = null;
        if (fileType == XLS) {
            wb = new HSSFWorkbook(in);
        } else {
            wb = new XSSFWorkbook(in);
        }
        for (int i = 0; i < wb.getNumberOfSheets(); i++) {
            Sheet sheet = wb.getSheetAt(i);
            List<List<Object>> rowList = new ArrayList<List<Object>>();
            for (int j = 1; j < sheet.getPhysicalNumberOfRows(); j++) {
                Row row = sheet.getRow(j);

                if (null != row) {
                    List<Object> cellList = new ArrayList<Object>();
                    for (int k = 0; k < row.getPhysicalNumberOfCells(); k++) {
                        if (row.getCell(k) != null) {
                            switch (row.getCell(k).getCellType()) {
                                case Cell.CELL_TYPE_BLANK:
                                    cellList.add("");
                                    break;
                                case Cell.CELL_TYPE_BOOLEAN:
                                    cellList.add(row.getCell(k).getBooleanCellValue());
                                    break;
                                case Cell.CELL_TYPE_STRING:
                                    cellList.add(row.getCell(k).getStringCellValue());
                                    break;
                                case Cell.CELL_TYPE_NUMERIC:
                                    if (DateUtil.isCellDateFormatted(row.getCell(k))) {
                                        Date date = DateUtil.getJavaDate(row.getCell(k).getNumericCellValue());
                                        cellList.add(date);
                                    } else {
                                        cellList.add(row.getCell(k).getNumericCellValue());
                                    }
                                    break;
                                case Cell.CELL_TYPE_FORMULA:
                                    cellList.add(row.getCell(k).getCellFormula());
                                    break;
                                case Cell.CELL_TYPE_ERROR:
                                    cellList.add("" + row.getCell(k).getErrorCellValue());
                                    break;
                            }
                        } else {
                            cellList.add(null);
                        }
                    }
                    rowList.add(cellList);
                }
            }
            fileData.add(rowList);
        }

        return fileData;
    }
}
